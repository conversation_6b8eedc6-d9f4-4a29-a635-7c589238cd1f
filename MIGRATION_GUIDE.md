# 🚀 Database Migration Guide

This guide covers the JavaScript-based database migration system for the Compliance Command Center DBOS application.

## 📋 Overview

The migration system uses Node.js scripts instead of direct SQL commands, providing:
- ✅ Better error handling and logging
- ✅ Environment-aware behavior
- ✅ Transaction safety
- ✅ Migration tracking
- ✅ Comprehensive verification

## 🗂️ Migration Scripts

### 1. `scripts/migrate.js` - Database Migrations
Handles database schema migrations with automatic tracking.

**Features:**
- Executes SQL migration files in order
- Tracks completed migrations in `dbos_migrations` table
- Transaction-safe execution (rollback on error)
- Skips already-executed migrations
- Detailed logging and timing

**Usage:**
```bash
# Run via npm
npm run migrate

# Run directly
node scripts/migrate.js

# Via DBOS (automatic)
dbos migrate
```

### 2. `scripts/predeploy.js` - Pre-deployment Setup
Handles pre-deployment tasks including mock data loading and verification.

**Features:**
- Environment-aware mock data loading
- Database schema verification
- Compliance rules validation
- System configuration checks

**Usage:**
```bash
# Run via npm
npm run predeploy

# Run directly
node scripts/predeploy.js

# Via DBOS (automatic during deployment)
dbos deploy
```

### 3. `scripts/postdeploy.js` - Post-deployment Verification
Performs comprehensive health checks and system validation.

**Features:**
- Database connectivity and performance checks
- Compliance rules validation
- System configuration verification
- Deployment report generation

**Usage:**
```bash
# Run via npm
npm run postdeploy

# Run directly
node scripts/postdeploy.js

# Via DBOS (automatic after deployment)
dbos deploy
```

## 📁 Migration Files

### Current Migrations

1. **`migrations/001_initial_schema.sql`**
   - Complete database schema (15+ tables)
   - Indexes and constraints
   - Triggers and functions
   - PostgreSQL extensions

2. **`migrations/002_initial_data.sql`**
   - Compliance standards configuration
   - Default compliance rules
   - System configuration settings
   - Initial performance metrics

### Adding New Migrations

1. Create a new migration file:
   ```bash
   # Follow naming convention: XXX_description.sql
   touch migrations/003_add_new_feature.sql
   ```

2. Write your migration SQL:
   ```sql
   -- Migration: 003_add_new_feature.sql
   -- Description: Add new feature tables
   
   CREATE TABLE new_feature (
       id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
       name VARCHAR(255) NOT NULL,
       created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
   );
   ```

3. The migration will be automatically picked up and executed in order.

## 🔧 Configuration

### DBOS Configuration (`dbos-config.yaml`)

```yaml
database:
  app_db_client: pg-node
  migrate:
    - "node scripts/migrate.js"

runtimeConfig:
  predeploy:
    - name: "Pre-deployment Setup"
      command: "node scripts/predeploy.js"
      description: "Load mock data and verify database setup"
  
  postdeploy:
    - name: "Post-deployment Verification"
      command: "node scripts/postdeploy.js"
      description: "Perform health checks and validate system configuration"
```

### Environment Variables

```bash
# Required
export DBOS_DATABASE_URL="postgresql://user:password@host:port/database"

# Optional
export NODE_ENV="development"  # or "production"
```

## 🧪 Testing Migrations

### 1. Test Individual Scripts

```bash
# Test migration script
npm run migrate

# Test predeploy script
npm run predeploy

# Test postdeploy script
npm run postdeploy
```

### 2. Test Full Deployment Flow

```bash
# Complete DBOS deployment (runs all scripts)
dbos deploy
```

### 3. Test Against Clean Database

```bash
# Drop and recreate database
dropdb dbos_kyc_demo
createdb dbos_kyc_demo

# Run full migration
npm run migrate
npm run predeploy
npm run postdeploy
```

## 📊 Migration Tracking

The system automatically tracks migrations in the `dbos_migrations` table:

```sql
SELECT * FROM dbos_migrations ORDER BY executed_at;
```

**Columns:**
- `filename`: Migration file name
- `executed_at`: When migration was executed
- `checksum`: File checksum (future use)
- `execution_time_ms`: How long migration took

## 🔍 Troubleshooting

### Common Issues

1. **Connection Errors**
   ```
   Error: Failed to connect to database
   ```
   - Check `DBOS_DATABASE_URL` environment variable
   - Verify database is running and accessible

2. **Migration Already Executed**
   ```
   Info: Skipping already executed migration
   ```
   - This is normal behavior
   - Check `dbos_migrations` table to see executed migrations

3. **SQL Syntax Errors**
   ```
   Error: Migration failed - syntax error
   ```
   - Check SQL syntax in migration file
   - Test SQL manually with `psql`

4. **Permission Errors**
   ```
   Error: permission denied for table
   ```
   - Ensure database user has required privileges
   - Check database connection string

### Debug Mode

Add debug logging to scripts by setting:
```bash
export DEBUG=true
```

## 🚀 Deployment Strategies

### Development
```bash
# Full setup with mock data
NODE_ENV=development dbos deploy
```

### Production
```bash
# Production deployment (no mock data)
NODE_ENV=production dbos deploy
```

### Staging
```bash
# Test migrations without deployment
npm run migrate
npm run predeploy
npm run postdeploy
```

## 📈 Best Practices

1. **Always test migrations** on a copy of production data
2. **Use transactions** for complex migrations (already handled)
3. **Keep migrations small** and focused
4. **Add rollback instructions** in migration comments
5. **Backup database** before running migrations in production
6. **Monitor execution time** for large migrations

## 🔄 Rollback Strategy

Currently, rollbacks must be done manually:

1. Create a rollback migration file
2. Write SQL to undo changes
3. Execute the rollback migration

Example rollback migration:
```sql
-- Migration: 004_rollback_feature.sql
-- Description: Rollback new feature addition

DROP TABLE IF EXISTS new_feature;
```
