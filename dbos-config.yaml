name: dbos_kyc_demo
database_url: ${DBOS_DATABASE_URL}
database:
  app_db_client: pg-node
runtimeConfig:
  # Pre-deployment hooks - run before the application starts
  predeploy:
    # 1. Ensure database schema is up to date
    - name: "Apply Database Schema"
      command: "psql ${DBOS_DATABASE_URL} -f database_schema.sql -v ON_ERROR_STOP=1"
      description: "Apply the main database schema with all tables and indexes"

    # 2. Apply initial configuration and data
    - name: "Apply Initial Configuration"
      command: "psql ${DBOS_DATABASE_URL} -f migrations/001_initial_schema.sql -v ON_ERROR_STOP=1"
      description: "Apply initial configuration data and compliance rules"

    # 3. Load mock data if not in production
    - name: "Load Mock Data"
      command: "npm run load-mock-data"
      description: "Load comprehensive mock data for development and testing"
      condition: "${NODE_ENV} != 'production'"

    # 4. Verify database setup
    - name: "Verify Database Setup"
      command: "psql ${DBOS_DATABASE_URL} -c \"SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public' AND table_type = 'BASE TABLE';\""
      description: "Verify that all required tables are present"

  # Post-deployment hooks - run after successful deployment
  postdeploy:
    # 1. Run database health check
    - name: "Database Health Check"
      command: "psql ${DBOS_DATABASE_URL} -c \"SELECT version(); SELECT NOW();\""
      description: "Verify database connectivity and basic functionality"

    # 2. Validate compliance rules are loaded
    - name: "Validate Compliance Rules"
      command: "psql ${DBOS_DATABASE_URL} -c \"SELECT COUNT(*) as rule_count FROM compliance_rules WHERE is_active = true;\""
      description: "Ensure compliance rules are properly loaded"

  # Application startup
  start:
    - "npm start"
